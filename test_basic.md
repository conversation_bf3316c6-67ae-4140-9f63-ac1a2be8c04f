# Project Documentation Summary

Generated from: `test_docs`
Extraction mode: full_context
Estimated tokens: ~749
Estimated reading time: 1 minutes
Total files: 5, Total snippets: 16

---

## File: sample.md

### Sample Documentation

Here's a simple Python function:

```python
def hello_world():
    """Print hello world."""
    print("Hello, World!")
    return True
```

### Sample Documentation

And here's some JavaScript:

```javascript
function greet(name) {
    console.log(`Hello, ${name}!`);
    return name;
}
```

### Sample Documentation

```javascript
function greet(name) {

```inline
Hello, ${name}!
```

    return name;
}
```

### Sample Documentation

```inline
inline code
```


## File: test.adoc

Here's a Python code block:

```python
def hello_world():
    """Print hello world."""
    print("Hello, World!")
    return True
```

== JavaScript Example

```javascript
function greet(name) {
    console.log(`Hello, ${name}!`);
    return name;
}
```

Some shell commands:

```
$ pip install generate-llms
$ generate-llms --help
```

[source,javascript]
----
function greet(name) {

```inline
Hello, ${name}!
```

    return name;
}


## File: test.html

### HTML Code Examples

    <p>Here's some JavaScript:</p>

```javascript

function greet(name) {
    console.log(`Hello, ${name}!`);
    return name;
}
```

    <p>And some Python:</p>
    <code>print("Hello World")</code>

### HTML Code Examples

    <p>And some Python:</p>

```python
print("Hello World")
```

    <script>
    // Inline script
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Page loaded');
    });
    </script>
</body>
</html>

### HTML Code Examples

    <p>And some Python:</p>
    <code>print("Hello World")</code>

```javascript
document.addEventListener('DOMContentLoaded', function() {
        console.log('Page loaded');
    });
```

</body>
</html>

### HTML Code Examples

    <p>Here's some JavaScript:</p>
    <pre><code class="language-javascript">
function greet(name) {

```inline
Hello, ${name}!
```

    return name;
}
    </code></pre>


## File: test.ipynb

### Test Jupyter Notebook

This is a test notebook with code and markdown cells.

```python
# Python code cell
def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

print(fibonacci(10))
```

### Python code cell

Let's do some data analysis:

```python
import pandas as pd
import numpy as np

# Create sample data
data = pd.DataFrame({
    'x': np.random.randn(100),
    'y': np.random.randn(100)
})

print(data.head())
```


## File: test.rst

### Test RST Document

--------------

```python
def hello_world():
    """Print hello world."""
    print("Hello, World!")
    return True
```

### Test RST Document

    $ pip install generate-llms
    $ generate-llms --help

```bash
echo "Hello from bash"
ls -la
```


---

## Processing Metadata

```json
{
  "generation": {
    "timestamp": "2025-08-16T03:23:09.871557",
    "tool_version": "1.0.0",
    "processing_mode": "full_context",
    "llm_rewrite_enabled": false
  },
  "source": {
    "root_path": "C:\\Users\\<USER>\\Desktop\\tie-fighter\\test_docs",
    "total_files_scanned": 5,
    "files_with_snippets": 0,
    "excluded_files": 0,
    "scan_depth": null
  },
  "content_metrics": {
    "total_snippets": 0,
    "snippets_by_language": {},
    "estimated_tokens": 0,
    "estimated_reading_time_minutes": 0
  },
  "quality_indicators": {
    "trust_score": 0.0,
    "completeness_score": 0.0,
    "code_coverage_percentage": 0.0
  },
  "processing": {
    "processing_time_seconds": 0.0,
    "errors": [],
    "warnings": []
  }
}
```
