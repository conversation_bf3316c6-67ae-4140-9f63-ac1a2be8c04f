"""
Command-line interface for generate-llms tool.
"""

import typer
import sys
from pathlib import Path
from typing import Optional, List
from enum import Enum

from .core.config import ProcessingConfig
from .core.processor import DocumentProcessor


class ExtractionMode(str, Enum):
    """Extraction mode options."""
    FULL_CONTEXT = "full_context"
    SNIPPETS_ONLY = "snippets_only"


class LLMBackend(str, Enum):
    """LLM backend options."""
    OLLAMA = "ollama"
    LLAMACPP = "llamacpp"
    TRANSFORMERS = "transformers"
    LMSTUDIO = "lmstudio"


app = typer.Typer(
    name="generate-llms",
    help="🚀 CLI tool that scans directories for documentation files and extracts code snippets optimized for LLM consumption.",
    add_completion=False,
    rich_markup_mode="rich",
    context_settings={"help_option_names": ["-h", "--help"]},
)


@app.command()
def process_docs(
    input_directory: Path = typer.Argument(
        ...,
        help="Path to directory containing documentation files",
        exists=True,
        file_okay=False,
        dir_okay=True,
        readable=True,
    ),
    output: Optional[Path] = typer.Option(
        None,
        "--output",
        "-o",
        help="Output file path (default: ./llms.txt)",
    ),
    mode: ExtractionMode = typer.Option(
        ExtractionMode.FULL_CONTEXT,
        "--mode",
        "-m",
        help="Extraction mode: full_context includes context, snippets_only extracts only code blocks",
    ),
    depth: Optional[int] = typer.Option(
        None,
        "--depth",
        "-d",
        help="Maximum scan depth (default: unlimited)",
        min=1,
    ),
    languages: Optional[str] = typer.Option(
        None,
        "--languages",
        "-l",
        help="Comma-separated list of languages to include (default: all)",
    ),
    exclude_patterns: Optional[str] = typer.Option(
        None,
        "--exclude-patterns",
        help="Comma-separated glob patterns for files/dirs to exclude",
    ),
    rewrite: bool = typer.Option(
        False,
        "--rewrite",
        help="Enable LLM-based content rewriting",
    ),
    llm_backend: LLMBackend = typer.Option(
        LLMBackend.OLLAMA,
        "--llm-backend",
        help="LLM backend for rewriting",
    ),
    llm_model: str = typer.Option(
        "llama2:7b",
        "--llm-model",
        help="Model name for rewriting (e.g., llama2:7b, codellama:13b)",
    ),
    llm_temperature: float = typer.Option(
        0.3,
        "--llm-temperature",
        help="LLM temperature for content generation (0.1-1.0)",
        min=0.1,
        max=1.0,
    ),
    llm_max_tokens: int = typer.Option(
        500,
        "--llm-max-tokens",
        help="Maximum tokens per LLM response",
        min=50,
        max=2000,
    ),
    lmstudio_base_url: str = typer.Option(
        "http://localhost:1234/v1",
        "--lmstudio-base-url",
        help="Base URL for LM Studio API server",
    ),
    lmstudio_timeout: int = typer.Option(
        60,
        "--lmstudio-timeout",
        help="Timeout in seconds for LM Studio requests",
    ),
    config: Optional[Path] = typer.Option(
        None,
        "--config",
        help="Path to configuration file",
        exists=True,
        file_okay=True,
        dir_okay=False,
        readable=True,
    ),
    verbose: bool = typer.Option(
        False,
        "--verbose",
        "-v",
        help="Enable detailed logging",
    ),
    dry_run: bool = typer.Option(
        False,
        "--dry-run",
        help="Show what would be processed without generating output",
    ),
) -> None:
    """
    🚀 Generate consolidated documentation file optimized for LLM consumption.

    This tool scans directories for documentation files, extracts code snippets,
    and generates a token-efficient markdown file perfect for feeding to LLMs.

    [bold green]Examples:[/bold green]

        [dim]# Basic usage - extract all code snippets with context[/dim]
        generate-llms ./docs

        [dim]# Snippets only mode for minimal token usage[/dim]
        generate-llms ./docs --mode snippets_only --output ./code_snippets.md

        [dim]# With LLM enhancement for better documentation[/dim]
        generate-llms ./project --rewrite --llm-backend ollama --llm-model llama2:7b

        [dim]# Using LM Studio for local LLM processing[/dim]
        generate-llms ./docs --rewrite --llm-backend lmstudio --llm-model "your-model-name"

        [dim]# Filter specific languages and exclude test files[/dim]
        generate-llms ./api_docs --languages python,javascript --exclude-patterns "*.test.*,temp/*"

        [dim]# Limit scan depth and use custom temperature[/dim]
        generate-llms ./src --depth 3 --llm-temperature 0.7 --verbose

        [dim]# Dry run to see what would be processed[/dim]
        generate-llms ./docs --dry-run --verbose

    [bold yellow]Tip:[/bold yellow] Use [code]generate-llms info ./path[/code] to analyze a directory first!
    """
    # Set default output path if not provided
    if output is None:
        output = Path("./llms.txt")
    
    # Parse language list
    language_list = None
    if languages:
        language_list = [lang.strip() for lang in languages.split(",")]
    
    # Parse exclude patterns
    exclude_pattern_list = None
    if exclude_patterns:
        exclude_pattern_list = [pattern.strip() for pattern in exclude_patterns.split(",")]
    
    # Create processing configuration
    processing_config = ProcessingConfig(
        input_directory=input_directory,
        output_file=output,
        mode=mode.value,
        max_depth=depth,
        languages=language_list,
        exclude_patterns=exclude_pattern_list,
        llm_rewrite_enabled=rewrite,
        llm_backend=llm_backend.value,
        llm_model=llm_model,
        llm_temperature=llm_temperature,
        llm_max_tokens=llm_max_tokens,
        lmstudio_base_url=lmstudio_base_url,
        lmstudio_timeout=lmstudio_timeout,
        config_file=config,
        verbose=verbose,
        dry_run=dry_run,
    )
    
    # Initialize and run processor
    processor = DocumentProcessor(processing_config)
    
    try:
        result = processor.process()
        
        if dry_run:
            typer.echo(f"[yellow]🔍 DRY RUN - No files will be modified[/yellow]")
            typer.echo(f"📁 Would process: [bold]{result.total_files_scanned}[/bold] files")
            typer.echo(f"📝 Found: [bold]{result.files_with_snippets}[/bold] files with code snippets")
            typer.echo(f"🔢 Would extract: [bold]{result.total_snippets}[/bold] code snippets")
            if result.estimated_tokens:
                typer.echo(f"📊 Estimated tokens: [bold]{result.estimated_tokens:,}[/bold]")
        else:
            typer.echo(f"[green]✅ Processing completed successfully![/green]")
            typer.echo(f"📁 Processed: [bold]{result.total_files_scanned}[/bold] files")
            typer.echo(f"📝 Extracted: [bold]{result.total_snippets}[/bold] code snippets from [bold]{result.files_with_snippets}[/bold] files")
            typer.echo(f"💾 Output written to: [bold cyan]{output}[/bold cyan]")
            typer.echo(f"📊 Estimated tokens: [bold]{result.estimated_tokens:,}[/bold]")

            # Show quality metrics if available
            if hasattr(result, 'trust_score') and result.trust_score > 0:
                typer.echo(f"🎯 Trust score: [bold]{result.trust_score:.1f}/10[/bold]")
            if hasattr(result, 'code_coverage_percentage') and result.code_coverage_percentage > 0:
                typer.echo(f"📈 Code coverage: [bold]{result.code_coverage_percentage:.1f}%[/bold]")

            # Show warnings if any
            if result.errors:
                typer.echo(f"[yellow]⚠️  {len(result.errors)} warnings/errors occurred[/yellow]")
            
    except Exception as e:
        typer.echo(f"❌ Error: {e}", err=True)
        raise typer.Exit(1)


@app.command()
def version():
    """Show version information."""
    typer.echo("generate-llms version 1.0.0")
    typer.echo("🚀 Local Documentation Code Snippet Aggregator")


@app.command()
def info(
    input_directory: Path = typer.Argument(
        ...,
        help="Path to directory to analyze",
        exists=True,
        file_okay=False,
        dir_okay=True,
        readable=True,
    ),
    depth: Optional[int] = typer.Option(
        None,
        "--depth",
        "-d",
        help="Maximum scan depth (default: unlimited)",
        min=1,
    ),
) -> None:
    """
    Show information about a directory without processing.

    Displays file counts, supported formats, and estimated processing time.
    """
    from .scanners import RecursiveFileScanner
    from .core.models import ProcessingConfig

    # Create minimal config for scanning
    config = ProcessingConfig(
        input_directory=input_directory,
        output_file=Path("dummy.txt"),
        max_depth=depth
    )

    scanner = RecursiveFileScanner()

    try:
        # Scan files
        files = scanner.scan(config)

        # Analyze file types
        file_types = {}
        total_size = 0

        for file_path in files:
            try:
                suffix = file_path.suffix.lower()
                file_types[suffix] = file_types.get(suffix, 0) + 1
                total_size += file_path.stat().st_size
            except Exception:
                continue

        # Display information
        typer.echo(f"📁 Directory: {input_directory}")
        typer.echo(f"📊 Total files: {len(files)}")
        typer.echo(f"💾 Total size: {total_size / 1024 / 1024:.1f} MB")

        if file_types:
            typer.echo("\n📋 File types:")
            for ext, count in sorted(file_types.items(), key=lambda x: x[1], reverse=True):
                typer.echo(f"  {ext or '(no extension)'}: {count} files")

        # Estimate processing time
        estimated_time = len(files) * 0.1  # Rough estimate
        typer.echo(f"\n⏱️  Estimated processing time: {estimated_time:.1f} seconds")

    except Exception as e:
        typer.echo(f"❌ Error analyzing directory: {e}", err=True)
        raise typer.Exit(1)


@app.command()
def validate_config(
    config_file: Path = typer.Argument(
        ...,
        help="Path to configuration file to validate",
        exists=True,
        file_okay=True,
        dir_okay=False,
        readable=True,
    ),
) -> None:
    """
    Validate a configuration file.

    Checks syntax and validates all configuration options.
    """
    from .core.config import load_config

    try:
        config = load_config(config_file)
        typer.echo(f"✅ Configuration file is valid: {config_file}")
        typer.echo(f"📋 Mode: {config.mode}")
        typer.echo(f"🔍 Max depth: {config.max_depth or 'unlimited'}")

        if config.languages:
            typer.echo(f"🌐 Languages: {', '.join(config.languages)}")

        if config.exclude_patterns:
            typer.echo(f"🚫 Exclude patterns: {', '.join(config.exclude_patterns)}")

    except Exception as e:
        typer.echo(f"❌ Configuration file is invalid: {e}", err=True)
        raise typer.Exit(1)


def main():
    """Entry point for the CLI application."""
    app()


if __name__ == "__main__":
    main()
