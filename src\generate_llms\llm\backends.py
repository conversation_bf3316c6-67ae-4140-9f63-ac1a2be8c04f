"""
LLM backend abstractions for content enhancement.
"""

import logging
import requests
from pathlib import Path
from typing import Optional, Dict, Any
from abc import ABC, abstractmethod
from dataclasses import dataclass

from ..core.models import ProcessingConfig


@dataclass
class LLMResponse:
    """Response from LLM backend."""
    
    enhanced_content: str
    success: bool
    error: Optional[str] = None
    metadata: Dict[str, Any] = None


class LLMBackend(ABC):
    """Abstract base class for LLM backends."""
    
    @abstractmethod
    def is_available(self) -> bool:
        """
        Check if the backend is available and properly configured.
        
        Returns:
            True if backend is ready to use
        """
        pass
    
    @abstractmethod
    def enhance_content(
        self, 
        content: str, 
        context: str, 
        config: ProcessingConfig
    ) -> LLMResponse:
        """
        Enhance content using the LLM backend.
        
        Args:
            content: Code snippet content to enhance
            context: Surrounding context for the snippet
            config: Processing configuration with LLM settings
            
        Returns:
            LLMResponse with enhanced content or error
        """
        pass


class OllamaBackend(LLMBackend):
    """
    Backend for Ollama local LLM server.
    """
    
    def __init__(self, base_url: str = "http://localhost:11434"):
        """
        Initialize Ollama backend.
        
        Args:
            base_url: Base URL for Ollama API
        """
        self.base_url = base_url
        self.logger = logging.getLogger(__name__)
    
    def is_available(self) -> bool:
        """Check if Ollama server is running and accessible."""
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=5)
            return response.status_code == 200
        except Exception as e:
            self.logger.debug(f"Ollama not available: {e}")
            return False
    
    def enhance_content(
        self, 
        content: str, 
        context: str, 
        config: ProcessingConfig
    ) -> LLMResponse:
        """
        Enhance content using Ollama API.
        
        Args:
            content: Code snippet to enhance
            context: Surrounding context
            config: Configuration with model and parameters
            
        Returns:
            LLMResponse with enhanced content
        """
        self.logger.debug(f"Enhancing content with Ollama model: {config.llm_model}")
        
        try:
            # Construct prompt for code enhancement
            prompt = self._build_enhancement_prompt(content, context)
            
            # Make API request
            payload = {
                "model": config.llm_model,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": config.llm_temperature,
                    "num_predict": config.llm_max_tokens,
                }
            }
            
            response = requests.post(
                f"{self.base_url}/api/generate",
                json=payload,
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                enhanced_content = result.get("response", "").strip()
                
                return LLMResponse(
                    enhanced_content=enhanced_content,
                    success=True,
                    metadata={"model": config.llm_model, "backend": "ollama"}
                )
            else:
                error_msg = f"Ollama API error: {response.status_code}"
                self.logger.error(error_msg)
                return LLMResponse(
                    enhanced_content=content,  # Fallback to original
                    success=False,
                    error=error_msg
                )
                
        except Exception as e:
            error_msg = f"Ollama enhancement failed: {e}"
            self.logger.error(error_msg)
            return LLMResponse(
                enhanced_content=content,  # Fallback to original
                success=False,
                error=error_msg
            )
    
    def _build_enhancement_prompt(self, content: str, context: str) -> str:
        """Build prompt for content enhancement."""
        return f"""Please improve the following code snippet and its documentation. 
Make the explanation clearer and more concise while preserving technical accuracy.

Context: {context}

Code:
```
{content}
```

Please provide:
1. The improved code (if any changes needed)
2. A clear, concise explanation of what the code does
3. Any important notes about usage or behavior

Keep the response focused and avoid unnecessary verbosity."""


class LlamaCppBackend(LLMBackend):
    """
    Backend for llama.cpp Python bindings.
    """
    
    def __init__(self):
        """Initialize llama.cpp backend."""
        self.logger = logging.getLogger(__name__)
        self.llm = None
    
    def is_available(self) -> bool:
        """Check if llama-cpp-python is available."""
        try:
            import llama_cpp
            return True
        except ImportError:
            self.logger.debug("llama-cpp-python not available")
            return False
    
    def enhance_content(
        self,
        content: str,
        context: str,
        config: ProcessingConfig
    ) -> LLMResponse:
        """
        Enhance content using llama.cpp.

        Args:
            content: Code snippet to enhance
            context: Surrounding context
            config: Configuration with model and parameters

        Returns:
            LLMResponse with enhanced content
        """
        self.logger.debug(f"Enhancing content with llama.cpp model: {config.llm_model}")

        try:
            import llama_cpp

            # Initialize model if not already loaded
            if self.llm is None:
                model_path = config.llm_model
                if not Path(model_path).exists():
                    raise FileNotFoundError(f"Model file not found: {model_path}")

                self.llm = llama_cpp.Llama(
                    model_path=model_path,
                    n_ctx=2048,  # Context window
                    n_threads=4,  # Number of threads
                    verbose=False
                )

            # Build prompt for enhancement
            prompt = self._build_enhancement_prompt(content, context)

            # Generate response
            response = self.llm(
                prompt,
                max_tokens=config.llm_max_tokens,
                temperature=config.llm_temperature,
                stop=["```", "---", "\n\n\n"],  # Stop sequences
                echo=False
            )

            enhanced_content = response['choices'][0]['text'].strip()

            return LLMResponse(
                enhanced_content=enhanced_content,
                success=True,
                metadata={"model": config.llm_model, "backend": "llamacpp"}
            )

        except Exception as e:
            error_msg = f"llama.cpp enhancement failed: {e}"
            self.logger.error(error_msg)
            return LLMResponse(
                enhanced_content=content,  # Fallback to original
                success=False,
                error=error_msg
            )

    def _build_enhancement_prompt(self, content: str, context: str) -> str:
        """Build prompt for content enhancement."""
        return f"""Please improve the following code snippet and its documentation.
Make the explanation clearer and more concise while preserving technical accuracy.

Context: {context}

Code:
```
{content}
```

Please provide:
1. The improved code (if any changes needed)
2. A clear, concise explanation of what the code does
3. Any important notes about usage or behavior

Keep the response focused and avoid unnecessary verbosity."""


class TransformersBackend(LLMBackend):
    """
    Backend for Hugging Face Transformers library.
    """
    
    def __init__(self):
        """Initialize Transformers backend."""
        self.logger = logging.getLogger(__name__)
        self.pipeline = None
    
    def is_available(self) -> bool:
        """Check if transformers library is available."""
        try:
            import transformers
            return True
        except ImportError:
            self.logger.debug("transformers library not available")
            return False
    
    def enhance_content(
        self,
        content: str,
        context: str,
        config: ProcessingConfig
    ) -> LLMResponse:
        """
        Enhance content using Transformers.

        Args:
            content: Code snippet to enhance
            context: Surrounding context
            config: Configuration with model and parameters

        Returns:
            LLMResponse with enhanced content
        """
        self.logger.debug(f"Enhancing content with Transformers model: {config.llm_model}")

        try:
            from transformers import pipeline, AutoTokenizer, AutoModelForCausalLM
            import torch

            # Initialize pipeline if not already loaded
            if self.pipeline is None:
                # Use a smaller model for documentation enhancement
                model_name = config.llm_model or "microsoft/DialoGPT-small"

                try:
                    # Try to load as text generation pipeline
                    self.pipeline = pipeline(
                        "text-generation",
                        model=model_name,
                        tokenizer=model_name,
                        device=0 if torch.cuda.is_available() else -1,
                        torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32
                    )
                except Exception as e:
                    self.logger.warning(f"Failed to load {model_name}, using default: {e}")
                    # Fallback to a known working model
                    self.pipeline = pipeline(
                        "text-generation",
                        model="gpt2",
                        device=-1  # CPU only for fallback
                    )

            # Build prompt for enhancement
            prompt = self._build_enhancement_prompt(content, context)

            # Generate response
            response = self.pipeline(
                prompt,
                max_length=len(prompt.split()) + config.llm_max_tokens,
                temperature=config.llm_temperature,
                do_sample=True,
                pad_token_id=self.pipeline.tokenizer.eos_token_id,
                num_return_sequences=1
            )

            # Extract generated text (remove the prompt)
            generated_text = response[0]['generated_text']
            enhanced_content = generated_text[len(prompt):].strip()

            return LLMResponse(
                enhanced_content=enhanced_content,
                success=True,
                metadata={"model": config.llm_model, "backend": "transformers"}
            )

        except Exception as e:
            error_msg = f"Transformers enhancement failed: {e}"
            self.logger.error(error_msg)
            return LLMResponse(
                enhanced_content=content,  # Fallback to original
                success=False,
                error=error_msg
            )

    def _build_enhancement_prompt(self, content: str, context: str) -> str:
        """Build prompt for content enhancement."""
        return f"""Improve this code documentation:

Context: {context}

Code:
{content}

Enhanced explanation:"""


class LMStudioBackend(LLMBackend):
    """
    Backend for LM Studio local API server.
    """

    def __init__(self, base_url: str = "http://localhost:1234/v1", timeout: int = 60):
        """
        Initialize LM Studio backend.

        Args:
            base_url: Base URL for LM Studio API (OpenAI-compatible endpoint)
            timeout: Request timeout in seconds
        """
        self.base_url = base_url
        self.timeout = timeout
        self.logger = logging.getLogger(__name__)

    def update_config(self, config: ProcessingConfig):
        """
        Update backend configuration from ProcessingConfig.

        Args:
            config: Processing configuration with LM Studio settings
        """
        self.base_url = config.lmstudio_base_url
        self.timeout = config.lmstudio_timeout

    def is_available(self) -> bool:
        """Check if LM Studio server is running and accessible."""
        try:
            # Check if server is running by listing models
            response = requests.get(
                f"{self.base_url}/models",
                headers={"Authorization": "Bearer lm-studio"},
                timeout=5
            )
            return response.status_code == 200
        except Exception as e:
            self.logger.debug(f"LM Studio not available: {e}")
            return False

    def get_available_models(self) -> list:
        """
        Get list of available models from LM Studio.

        Returns:
            List of model identifiers, empty list if unavailable
        """
        try:
            response = requests.get(
                f"{self.base_url}/models",
                headers={"Authorization": "Bearer lm-studio"},
                timeout=10
            )

            if response.status_code == 200:
                result = response.json()
                if "data" in result:
                    return [model["id"] for model in result["data"]]

            return []
        except Exception as e:
            self.logger.debug(f"Failed to get LM Studio models: {e}")
            return []

    def enhance_content(
        self,
        content: str,
        context: str,
        config: ProcessingConfig
    ) -> LLMResponse:
        """
        Enhance content using LM Studio API.

        Args:
            content: Code snippet to enhance
            context: Surrounding context
            config: Configuration with model and parameters

        Returns:
            LLMResponse with enhanced content
        """
        self.logger.debug(f"Enhancing content with LM Studio model: {config.llm_model}")

        # Update configuration from ProcessingConfig
        self.update_config(config)

        # Validate model availability if possible
        if not self._validate_model_availability(config.llm_model):
            self.logger.warning(f"Model '{config.llm_model}' may not be available in LM Studio")

        try:
            # Build messages for chat completion
            messages = self._build_chat_messages(content, context)

            # Make API request to OpenAI-compatible endpoint
            payload = {
                "model": config.llm_model,
                "messages": messages,
                "temperature": config.llm_temperature,
                "max_tokens": config.llm_max_tokens,
                "stream": False
            }

            response = requests.post(
                f"{self.base_url}/chat/completions",
                json=payload,
                headers={
                    "Content-Type": "application/json",
                    "Authorization": "Bearer lm-studio"  # LM Studio's standard API key
                },
                timeout=self.timeout
            )

            if response.status_code == 200:
                try:
                    result = response.json()
                except ValueError as e:
                    error_msg = f"Invalid JSON response from LM Studio: {e}"
                    self.logger.error(error_msg)
                    return LLMResponse(
                        enhanced_content=content,
                        success=False,
                        error=error_msg
                    )

                # Handle OpenAI-compatible response format
                if "choices" in result and len(result["choices"]) > 0:
                    choice = result["choices"][0]
                    if "message" in choice and "content" in choice["message"]:
                        enhanced_content = choice["message"]["content"].strip()

                        # Extract usage metadata if available
                        usage_metadata = {}
                        if "usage" in result:
                            usage_metadata = {
                                "prompt_tokens": result["usage"].get("prompt_tokens", 0),
                                "completion_tokens": result["usage"].get("completion_tokens", 0),
                                "total_tokens": result["usage"].get("total_tokens", 0)
                            }

                        return LLMResponse(
                            enhanced_content=enhanced_content,
                            success=True,
                            metadata={
                                "model": config.llm_model,
                                "backend": "lmstudio",
                                "usage": usage_metadata
                            }
                        )

                # If response format is unexpected, log and fallback
                self.logger.warning(f"Unexpected response format from LM Studio: {result}")
                return LLMResponse(
                    enhanced_content=content,
                    success=False,
                    error="Unexpected response format from LM Studio API"
                )
            else:
                error_msg = f"LM Studio API error: {response.status_code}"
                if response.text:
                    try:
                        error_detail = response.json()
                        if "error" in error_detail:
                            error_msg += f" - {error_detail['error']}"
                    except:
                        error_msg += f" - {response.text[:200]}"

                self.logger.error(error_msg)
                return LLMResponse(
                    enhanced_content=content,  # Fallback to original
                    success=False,
                    error=error_msg
                )

        except requests.exceptions.ConnectionError as e:
            error_msg = f"Cannot connect to LM Studio server at {self.base_url}: {e}"
            self.logger.error(error_msg)
            return LLMResponse(
                enhanced_content=content,
                success=False,
                error=error_msg
            )
        except requests.exceptions.Timeout as e:
            error_msg = f"LM Studio request timeout after {self.timeout}s: {e}"
            self.logger.error(error_msg)
            return LLMResponse(
                enhanced_content=content,
                success=False,
                error=error_msg
            )
        except Exception as e:
            error_msg = f"LM Studio enhancement failed: {e}"
            self.logger.error(error_msg)
            return LLMResponse(
                enhanced_content=content,  # Fallback to original
                success=False,
                error=error_msg
            )

    def _build_chat_messages(self, content: str, context: str) -> list:
        """Build OpenAI-compatible chat messages for content enhancement."""
        system_prompt = """You are a technical documentation expert. Your task is to improve code snippets and their documentation. Make explanations clearer and more concise while preserving technical accuracy.

Please provide:
1. The improved code (if any changes needed)
2. A clear, concise explanation of what the code does
3. Any important notes about usage or behavior

Keep the response focused and avoid unnecessary verbosity."""

        user_prompt = f"""Please improve the following code snippet and its documentation.

Context: {context}

Code:
```
{content}
```"""

        return [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]

    def _validate_model_availability(self, model_name: str) -> bool:
        """
        Validate if the specified model is available in LM Studio.

        Args:
            model_name: Name of the model to validate

        Returns:
            True if model is available, False otherwise
        """
        try:
            available_models = self.get_available_models()
            return model_name in available_models
        except Exception as e:
            self.logger.debug(f"Could not validate model availability: {e}")
            return True  # Assume available if we can't check


class LLMBackendFactory:
    """Factory for creating LLM backends."""

    def __init__(self):
        """Initialize the factory."""
        self.logger = logging.getLogger(__name__)

    def create_backend(self, backend_name: str) -> Optional[LLMBackend]:
        """
        Create appropriate LLM backend.

        Args:
            backend_name: Name of backend to create

        Returns:
            LLMBackend instance or None if not available
        """
        backend_name = backend_name.lower()

        if backend_name == "ollama":
            backend = OllamaBackend()
            if backend.is_available():
                return backend
            else:
                self.logger.warning("Ollama backend not available")

        elif backend_name == "llamacpp":
            backend = LlamaCppBackend()
            if backend.is_available():
                return backend
            else:
                self.logger.warning("llama.cpp backend not available")

        elif backend_name == "transformers":
            backend = TransformersBackend()
            if backend.is_available():
                return backend
            else:
                self.logger.warning("Transformers backend not available")

        elif backend_name == "lmstudio":
            # Use default configuration for factory creation
            # The actual configuration will be passed during enhance_content calls
            backend = LMStudioBackend()
            if backend.is_available():
                return backend
            else:
                self.logger.warning("LM Studio backend not available")

        else:
            self.logger.error(f"Unknown backend: {backend_name}")

        return None
