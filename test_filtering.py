#!/usr/bin/env python3
"""
Test script to verify code filtering functionality.
"""

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from generate_llms.core.models import ProcessingConfig, ParsedContent, CodeSnippet
from generate_llms.extractors.code_extractor import RegexCodeExtractor

def test_meaningful_code_filtering():
    """Test that meaningful code is preserved and non-meaningful code is filtered."""
    
    # Create test configuration
    config = ProcessingConfig(
        input_directory=Path("."),
        output_file=Path("test.md"),
        enable_code_filtering=True
    )
    
    extractor = RegexCodeExtractor()
    
    # Test cases: (content, language, should_be_meaningful)
    test_cases = [
        # Meaningful code - should be kept
        ("def hello_world():\n    print('Hello, World!')", "python", True),
        ("function fetchData() {\n    return fetch('/api/data');\n}", "javascript", True),
        ("if (user.isAuthenticated()) {\n    showDashboard();\n}", "javascript", True),
        ("SELECT * FROM users WHERE active = 1", "sql", True),
        
        # Non-meaningful code - should be filtered
        ("<div class='container'>\n    <p>Hello</p>\n</div>", "html", False),
        (".container {\n    color: red;\n    font-size: 14px;\n}", "css", False),
        ("<!-- This is just a comment -->", "html", False),
        
        # Edge cases
        ("", "python", False),  # Empty content
        ("# Just a comment", "python", False),  # Only comments
        ("console.log('test')", "javascript", True),  # Simple but meaningful
        
        # HTML with meaningful content - should be kept
        ("<form onsubmit='handleSubmit()'>\n    <input ng-model='data'>\n</form>", "html", True),
        
        # CSS with meaningful content - should be kept
        ("@media (max-width: 768px) {\n    .container { display: none; }\n}", "css", True),
    ]
    
    print("Testing code filtering...")
    
    for i, (content, language, should_be_meaningful) in enumerate(test_cases):
        snippet = CodeSnippet(
            content=content,
            language=language,
            file_path=Path("test.md")
        )
        
        is_meaningful = extractor._is_meaningful_code(snippet)
        
        status = "✓" if is_meaningful == should_be_meaningful else "✗"
        print(f"{status} Test {i+1}: {language} - Expected: {should_be_meaningful}, Got: {is_meaningful}")
        
        if is_meaningful != should_be_meaningful:
            print(f"    Content: {content[:50]}...")
    
    print("\nTesting full filtering pipeline...")
    
    # Test full filtering pipeline
    test_snippets = [
        CodeSnippet(content="def test():\n    pass", language="python", file_path=Path("test.py")),
        CodeSnippet(content="<div>Hello</div>", language="html", file_path=Path("test.html")),
        CodeSnippet(content="color: red;", language="css", file_path=Path("test.css")),
        CodeSnippet(content="console.log('test')", language="javascript", file_path=Path("test.js")),
    ]
    
    filtered = extractor._filter_meaningful_code(test_snippets, config)
    
    print(f"Original snippets: {len(test_snippets)}")
    print(f"Filtered snippets: {len(filtered)}")
    print("Remaining languages:", [s.language for s in filtered])
    
    # Test with filtering disabled
    config.enable_code_filtering = False
    filtered_disabled = extractor._filter_meaningful_code(test_snippets, config)
    print(f"With filtering disabled: {len(filtered_disabled)} snippets")

if __name__ == "__main__":
    test_meaningful_code_filtering()
